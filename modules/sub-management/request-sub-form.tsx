import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm, useWatch } from "react-hook-form";
import { View } from "react-native";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormField,
  FormInput,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";

import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";

import { ComboBox } from "~/components/modules/common/Combo-box";
import { MultiComboBox } from "~/components/modules/common/Multi-combo-box";
import { SUB_REASONS } from "./constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";

import { DatePicker } from "~/components/modules/common/date-picker";

import { useFetchClasses } from "./queries/useFetchClasses";

export const RequestSubSchema = z.object({
  date: z.date({
    message: "Date is required",
  }),
  classes: z
    .array(
      z.object(
        {
          id: z.string(),
          title: z.string(),
        },
        { message: "Class is required" }
      )
    )
    .min(1, { message: "At least one class is required" }),
  reason: z.object(
    {
      id: z.string(),
      title: z.string(),
    },
    { message: "Reason is required" }
  ),
  message: z.string().optional(),
});

export const RequestSubManagementForm = ({
  onSubmit,
  isLoading,
}: {
  onSubmit: (data: z.infer<typeof RequestSubSchema>) => void;
  isLoading?: boolean;
}) => {
  const form = useForm<z.infer<typeof RequestSubSchema>>({
    resolver: zodResolver(RequestSubSchema),
    defaultValues: {
      date: new Date(),
      classes: [],
    },
  });

  const selectedValues = useWatch({
    control: form.control,
  });

  const { data = [], isPending } = useFetchClasses({
    date: selectedValues.date,
  });

  const resetClassesValue = () => {
    form.setValue("classes", [], { shouldDirty: true });
  };

  return (
    <AutocompleteDropdownContextProvider>
      <KeyboardAwareScrollView
        bottomOffset={10}
        contentContainerStyle={{
          gap: 8,
          paddingBottom: 100,
          paddingHorizontal: 16,
          paddingTop: 16,
        }}
      >
        <Form {...form}>
          <View className="flex-1 flex relative">
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <DatePicker
                  value={field.value}
                  onChange={(date) => {
                    field.onChange(date);
                    resetClassesValue();
                  }}
                  label="Date"
                />
              )}
            />
            <FormField
              control={form.control}
              name="classes"
              render={({ field }) => {
                // Convert the form value to the expected type
                const value = Array.isArray(selectedValues.classes)
                  ? (selectedValues.classes.map((item) => ({
                      id: item.id || "",
                      title: item.title || "",
                    })) as AutocompleteDropdownItem[])
                  : ([] as AutocompleteDropdownItem[]);
                return (
                  <View className="mb-1 space-y-2">
                    <FormLabel>Classes (select one or more)</FormLabel>
                    <View style={{ minHeight: 50 }}>
                      <MultiComboBox
                        data={data}
                        placeholder="Select classes"
                        selectedItems={value}
                        onSelect={(items: AutocompleteDropdownItem[]) => {
                          field.onChange(items);
                        }}
                      />
                    </View>
                    <FormMessage />
                  </View>
                );
              }}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => {
                const value = field.value as unknown as Record<string, string>;
                return (
                  <View className="mt-0 flex-1 space-y-2">
                    <FormLabel>Reason</FormLabel>
                    <View className="h-14">
                      <ComboBox
                        direction="up"
                        data={SUB_REASONS}
                        placeholder="Select reasons"
                        onSelect={field.onChange}
                        initialValue={value?.id ?? ""}
                        value={value?.title ?? ""}
                      />
                    </View>
                    <FormMessage />
                  </View>
                );
              }}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <View className="mt-2 flex-1">
                  <FormLabel>
                    Notes for Supervisors or Other Instructors
                  </FormLabel>
                  <FormInput
                    textContentType="oneTimeCode"
                    placeholder="Enter message"
                    autoCapitalize="none"
                    secureTextEntry
                    onChangeText={field.onChange}
                    multiline={true}
                    numberOfLines={10}
                    style={{
                      marginTop: 10,
                      height: 100,
                      textAlignVertical: "top",
                    }}
                    {...field}
                    value={field.value || ""}
                  />
                </View>
              )}
            />

            <View className="flex flex-col justify-between  mt-2">
              <Button
                className="bg-[#002966]"
                disabled={isLoading}
                isLoading={isLoading}
                onPress={form.handleSubmit(onSubmit)}
                label="Save"
              />
            </View>
          </View>
        </Form>
      </KeyboardAwareScrollView>
    </AutocompleteDropdownContextProvider>
  );
};
