import { useMutation } from "@tanstack/react-query";

import { useOptimisticUpdate } from "./useOptimisticUpdate";
import { showSuccessToast } from "~/components/toast";
import { api } from "~/lib/api";

export const promoteWaitlistToClass = async (id: number) => {
  try {
    const response = await api
      .get<{ message: string; success: boolean }>("classes/waitlists/promote", {
        json: {
          waitlist_id: id,
        },
      })
      .json();
    return await response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to promote waitlist",
    };
  }
};

export const useWaitlistToClass = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: promoteWaitlistToClass,
    onSuccess: async (data) => {
      if (data.success) {
        await invalidateQueries();
        onSuccess?.();
        return showSuccessToast(data.message);
      }
    },
  });
};
