import { useQueryClient } from "@tanstack/react-query";
import { useLocalSearchParams } from "expo-router";
import { formatClassDate } from "../utils";
import { useSession } from "~/modules/login/auth-provider";

export function useOptimisticUpdate() {
  const {
    id,
    selectedDate,
    date: paramDate,
  } = useLocalSearchParams<{
    id: string;
    selectedDate: string;
    date?: string;
  }>();

  const { data: sessionData } = useSession();

  const queryClient = useQueryClient();

  const date = formatClassDate(paramDate);

  return async () => {
    await queryClient.invalidateQueries({
      queryKey: [{ entity_id: id, date }],
    });
    await queryClient.invalidateQueries({
      queryKey: [{ waitlistId: id, date }],
    });

    await queryClient.invalidateQueries({
      queryKey: [sessionData?.token, date],
    });
    await queryClient.invalidateQueries({
      queryKey: ["appointments", selectedDate ?? ""],
    });

    await queryClient.invalidateQueries({
      queryKey: ["reservations"],
    });
  };
}
