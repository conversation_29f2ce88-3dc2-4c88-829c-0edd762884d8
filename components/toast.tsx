import Toast, { ToastOptions } from "react-native-root-toast";

export const showSuccessToast = (
  message: string,
  position?: ToastOptions["position"]
) => {
  return Toast.show(message, {
    duration: Toast.durations.LONG,
    position: position || Toast.positions.CENTER,
    shadow: true,
    animation: true,
    backgroundColor: "green",
  });
};

export const showErrorToast = (
  message: string,
  position?: ToastOptions["position"]
) => {
  return Toast.show(message, {
    duration: Toast.durations.LONG,
    position: position || Toast.positions.CENTER,
    shadow: true,
    animation: true,
    hideOnPress: true,
    backgroundColor: "red",
  });
};
