import { Image } from "expo-image";
import { <PERSON> } from "expo-router";
import { PropsWithChildren } from "react";
import { ImageBackground, View } from "react-native";
import { Text } from "~/components/ui/text";

export const LoginBackground = ({ children }: PropsWithChildren) => {
  return (
    <ImageBackground
      source={require("../../../assets/images/login.png")}
      resizeMode={"cover"}
      style={{ flex: 1, width: "100%" }}
      resizeMethod="auto"
    >
      <Image
        source={require("../../../assets/images/login-logo.svg")}
        style={{
          height: 80,
          width: 100,
          display: "flex",
          position: "relative",
          top: 100,
          left: "50%",
          transform: [{ translateX: -50 }],
        }}
        contentFit="contain"
      />
      {children}
      <View
        style={{
          position: "absolute",
          bottom: 25,
          left: 0,
          right: 0,
          padding: 16,
        }}
      >
        <Text className="pl-6 pr-6 text-white mb-3 font-bold">
          Contact your Upace administrator if you do not have an account.
        </Text>
        <Text className="pl-6 pr-6 text-white font-bold">
          By logging in, you are agreeing to our{" "}
          <Link
            className="underline"
            href={
              "https://rachel-koretsky.squarespace.com/terms-and-privacy-new"
            }
          >
            terms of service and privacy policy.
          </Link>
        </Text>
      </View>
    </ImageBackground>
  );
};
