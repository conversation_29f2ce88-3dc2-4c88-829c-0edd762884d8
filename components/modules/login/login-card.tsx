import React, { PropsWithChildren } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  useWindowDimensions,
} from "react-native";

const { width } = Dimensions.get("window");
const CARD_WIDTH = Math.min(600, width - 32);

export const LoginCard = ({ children }: PropsWithChildren) => {
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const isLandscape = windowWidth > windowHeight;

  const cardWidth = Math.min(600, windowWidth * 0.9);
  const cardPadding = windowWidth < 380 ? 20 : 40;

  // Refactor to remove Stylesheet.create from here
  const dynamicStyles = StyleSheet.create({
    card: {
      width: cardWidth,
      padding: cardPadding,
      maxHeight: isLandscape ? windowHeight * 0.9 : undefined,
    },
  });

  return (
    <View style={styles.container}>
      <View style={[styles.card, dynamicStyles.card]}>
        <View style={styles.content}>{children}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 10,
    paddingRight: 10,
  },
  card: {
    width: CARD_WIDTH,
    padding: 40,
    backgroundColor: "white",
    borderRadius: 48,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
    position: "relative",
  },

  content: {
    gap: 5,
    zIndex: 2,
  },
});

export default LoginCard;
