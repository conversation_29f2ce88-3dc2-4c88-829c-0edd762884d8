import React, { useState, ReactNode, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  ScrollView,
  Modal,
  Pressable,
  TextStyle,
  StyleProp,
} from "react-native";
import { ChevronLeft, ChevronRight, Calendar, X } from "lucide-react-native";
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import {
  format,
  setYear,
  setMonth,
  getYear,
  getMonth,
  eachMonthOfInterval,
  startOfYear,
  endOfYear,
  isSameMonth,
  isSameYear,
} from "date-fns";

interface Theme {
  primary: string;
  background: string;
  text: string;
  textSecondary: string;
  border: string;
  selected: string;
  selectedText: string;
}

interface MonthDatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minYear?: number;
  maxYear?: number;
  trigger?: ReactNode;
  triggerContainerStyle?: string;
  triggerTextStyle?: string;
  theme?: Partial<Theme>;
  formatDate?: (date: Date) => string;
  modalTitle?: string;
  modalTitleStyle?: StyleProp<TextStyle>;
  monthFormat?: "short" | "long";
  showIcon?: boolean;
  iconColor?: string;
  iconSize?: number;
}

const defaultTheme: Theme = {
  primary: "#3b82f6",
  background: "#ffffff",
  text: "#1f2937",
  textSecondary: "#4b5563",
  border: "#f3f4f6",
  selected: "#3b82f6",
  selectedText: "#ffffff",
};

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

export function MonthDatePicker({
  value,
  onChange,
  minYear = getYear(new Date()) - 2,
  maxYear = getYear(new Date()) + 5,
  trigger,
  theme: customTheme,
  modalTitle = "Select Date",
  modalTitleStyle,
  monthFormat = "short",
  showIcon = true,
  iconSize = 20,
}: MonthDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value);
  const [previewDate, setPreviewDate] = useState(value);
  const [showYearPicker, setShowYearPicker] = useState(false);

  const theme: Theme = { ...defaultTheme, ...customTheme };

  const years = Array.from(
    { length: maxYear - minYear + 1 },
    (_, i) => minYear + i
  );

  useEffect(() => {
    setPreviewDate(value);
  }, [value]);

  const months = eachMonthOfInterval({
    start: startOfYear(previewDate),
    end: endOfYear(previewDate),
  });

  const handleYearSelect = (year: number) => {
    const newDate = setYear(previewDate, year);
    setPreviewDate(newDate);
    setShowYearPicker(false);
  };

  const navigateYear = (direction: "prev" | "next") => {
    const currentYear = getYear(previewDate);
    const newYear = direction === "prev" ? currentYear - 1 : currentYear + 1;

    if (direction === "prev" && newYear < minYear) return;
    if (direction === "next" && newYear > maxYear) return;

    const newDate = setYear(previewDate, newYear);
    setPreviewDate(newDate);
  };

  const yearButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: withSpring(showYearPicker ? 1.1 : 1) }],
  }));

  const handleClose = () => {
    setIsOpen(false);
    setShowYearPicker(false);
    setPreviewDate(selectedDate);
  };

  const handleMonthSelect = (monthIndex: number) => {
    const newDate = setMonth(previewDate, monthIndex);
    setPreviewDate(newDate);
  };

  const handleConfirm = () => {
    setSelectedDate(previewDate);
    onChange(previewDate);
    handleClose();
  };

  const renderTrigger = () => {
    if (trigger) {
      return (
        <TouchableOpacity onPress={() => setIsOpen(true)}>
          {trigger}
        </TouchableOpacity>
      );
    }

    return (
      <TouchableOpacity onPress={() => setIsOpen(true)}>
        {showIcon && <Calendar size={24} color={"orange"} className="mr-2" />}
      </TouchableOpacity>
    );
  };

  return (
    <>
      {renderTrigger()}

      <Modal
        visible={isOpen}
        transparent
        animationType="fade"
        onRequestClose={handleClose}
      >
        <Pressable
          className="flex-1 bg-black/25 justify-center items-center"
          onPress={handleClose}
        >
          <Pressable className="w-10/12 max-w-md">
            <View className="bg-white rounded-2xl p-2 shadow-xl ">
              <View className="flex-row justify-between items-center mb-4">
                <View className="w-8" />
                {modalTitle && (
                  <Text
                    className="text-lg font-semibold text-gray-900 flex-1 text-center"
                    style={modalTitleStyle}
                  >
                    {modalTitle}
                  </Text>
                )}
                <TouchableOpacity onPress={handleClose} className="p-1">
                  <X size={20} color={theme.textSecondary} />
                </TouchableOpacity>
              </View>

              <View className="flex-row justify-between items-center mb-1">
                <TouchableOpacity
                  onPress={() => navigateYear("prev")}
                  className="p-1"
                >
                  <ChevronLeft size={20} color={theme.textSecondary} />
                </TouchableOpacity>

                <AnimatedTouchableOpacity
                  onPress={() => setShowYearPicker(!showYearPicker)}
                  className="px-3 py-1"
                  style={yearButtonStyle}
                >
                  <Text className="text-base font-semibold text-gray-900">
                    {format(previewDate, "yyyy")}
                  </Text>
                </AnimatedTouchableOpacity>

                <TouchableOpacity
                  onPress={() => navigateYear("next")}
                  className="p-1"
                >
                  <ChevronRight size={20} color={theme.textSecondary} />
                </TouchableOpacity>
              </View>

              {!showYearPicker ? (
                <Animated.View
                  entering={FadeIn}
                  exiting={FadeOut}
                  className="flex-row flex-wrap justify-between"
                >
                  {months.map((month) => (
                    <TouchableOpacity
                      key={format(month, "MMM")}
                      onPress={() => handleMonthSelect(getMonth(month))}
                      className={`w-[21%] aspect-[1.9] p-2 font-bold border- dark:border-white justify-center items-center mb-0.5 rounded-lg ${
                        isSameMonth(previewDate, month)
                          ? "bg-[#002966]  dark:bg-cyan-300"
                          : "dark:bg-gray-800"
                      }`}
                    >
                      <Text
                        className={`text-sm ${
                          isSameMonth(previewDate, month)
                            ? "text-white dark:text-base"
                            : "text-gray-600 dark:text-white"
                        }`}
                      >
                        {monthFormat === "short"
                          ? format(month, "MMM").toUpperCase()
                          : format(month, "MMMM")}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </Animated.View>
              ) : (
                <Animated.View
                  entering={FadeIn}
                  exiting={FadeOut}
                  className="h-[120px] rounded-lg overflow-hidden"
                >
                  <ScrollView
                    className="flex-1"
                    showsVerticalScrollIndicator={Platform.OS === "web"}
                  >
                    {years.map((year) => (
                      <TouchableOpacity
                        key={year}
                        onPress={() => handleYearSelect(year)}
                        className={`p-2 items-center mb-0.5 rounded-lg ${
                          isSameYear(previewDate, new Date(year, 0))
                            ? "bg-primary dark:bg-cyan-300"
                            : "bg-gray-100"
                        }`}
                      >
                        <Text
                          className={`text-sm ${
                            isSameYear(previewDate, new Date(year, 0))
                              ? "text-white"
                              : "text-gray-600"
                          }`}
                        >
                          {year}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </Animated.View>
              )}

              <TouchableOpacity
                onPress={handleConfirm}
                className="w-24 py-1.5 rounded-lg bg-[#002966] items-center self-end mb-1"
              >
                <Text className="text-white font-medium">Confirm</Text>
              </TouchableOpacity>
            </View>
          </Pressable>
        </Pressable>
      </Modal>
    </>
  );
}
