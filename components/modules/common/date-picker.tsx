import React from "react";
import { View } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { FormInput, FormLabel, FormMessage } from "~/components/ui/form";
import { format } from "date-fns";

import Fontisto from "@expo/vector-icons/Fontisto";
import { useColorScheme } from "~/lib/useColorScheme";
import { noop } from "lodash/fp";

export const DatePicker = ({
  label,
  value,
  onChange,
}: {
  label: string;
  value: Date;
  onChange: (date: Date) => void;
}) => {
  const { isDarkColorScheme } = useColorScheme();

  const [isDatePickerVisible, setDatePickerVisibility] = React.useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    onChange(date);
    hideDatePicker();
  };

  return (
    <React.Fragment>
      <FormLabel>{label}</FormLabel>
      <View className="mb-2">
        <FormInput
          name=""
          textContentType="oneTimeCode"
          value={format(value, "EEE MMM d, yyyy")}
          onBlur={noop}
          onChange={noop}
          className="p-4"
          onPress={showDatePicker}
          style={{ height: 50 }}
        />

        <TouchableOpacity
          onPress={showDatePicker}
          style={{
            position: "absolute",
            right: 10,
            top: "50%",
            transform: [{ translateY: -40 }],
          }}
        >
          <Fontisto
            name="date"
            size={20}
            color={isDarkColorScheme ? "white" : "dark"}
            onPress={showDatePicker}
          />
        </TouchableOpacity>
        <DateTimePickerModal
          date={value}
          locale="en-US"
          isDarkModeEnabled={isDarkColorScheme}
          minimumDate={new Date()}
          display="inline"
          isVisible={isDatePickerVisible}
          mode="date"
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
          confirmTextIOS="Confirm"
          cancelTextIOS=""
          customCancelButtonIOS={() => <></>}
        />
        <FormMessage />
      </View>
    </React.Fragment>
  );
};
