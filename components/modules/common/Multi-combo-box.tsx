import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  TouchableWithoutFeedback,
  Dimensions,
} from "react-native";
import { AutocompleteDropdownItem } from "react-native-autocomplete-dropdown";
import { useColorScheme } from "~/lib/useColorScheme";
import { X, ChevronDown, Check } from "lucide-react-native";
import { cn } from "~/lib/utils";

export const MultiComboBox = ({
  data,
  selectedItems = [],
  onSelect,
  placeholder,
}: {
  data: AutocompleteDropdownItem[];
  selectedItems?: AutocompleteDropdownItem[];
  onSelect?: (items: AutocompleteDropdownItem[]) => void;
  placeholder?: string;
}) => {
  const { isDarkColorScheme } = useColorScheme();
  const [selected, setSelected] =
    useState<AutocompleteDropdownItem[]>(selectedItems);
  const [showSelectedModal, setShowSelectedModal] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [comboBoxLayout, setComboBoxLayout] = useState({
    y: 0,
    height: 0,
    x: 0,
    width: 0,
  });

  // We no longer filter out selected items from the available items
  // Instead, we'll show all items and indicate which ones are selected

  const handleSelect = (item: AutocompleteDropdownItem | null) => {
    if (item) {
      // Check if item is already selected
      const isAlreadySelected = selected.some(
        (selectedItem) => selectedItem.id === item.id
      );

      let newSelected;
      if (isAlreadySelected) {
        // If already selected, remove it
        newSelected = selected.filter(
          (selectedItem) => selectedItem.id !== item.id
        );
      } else {
        // If not selected, add it
        newSelected = [...selected, item];
      }

      setSelected(newSelected);

      // Call the parent onSelect callback
      if (onSelect) {
        onSelect(newSelected);
      }

      // Close the dropdown after selection
      setDropdownVisible(false);
    }
  };

  const handleRemove = (itemToRemove: AutocompleteDropdownItem) => {
    // Remove from selected
    const newSelected = selected.filter((item) => item.id !== itemToRemove.id);
    setSelected(newSelected);

    // Call the parent onSelect callback
    if (onSelect) {
      onSelect(newSelected);
    }
  };

  // Modal to show all selected items
  const renderSelectedItemsModal = () => {
    return (
      <Modal
        visible={showSelectedModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSelectedModal(false)}
      >
        <TouchableOpacity
          className="flex-1 bg-black/50 justify-center items-center p-5"
          activeOpacity={1}
          onPress={() => setShowSelectedModal(false)}
        >
          <View
            className={cn(
              "w-[90%] max-h-[80%] rounded-xl p-4 shadow-md",
              isDarkColorScheme ? "bg-[#222]" : "bg-white"
            )}
          >
            <Text
              className={cn(
                "text-lg font-bold mb-4 text-center",
                isDarkColorScheme ? "text-white" : "text-black"
              )}
            >
              Selected Items
            </Text>
            <FlatList
              data={selected}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View className="flex-row items-center py-2 border-b border-gray-200">
                  <Text
                    className={cn(
                      "flex-1 text-sm",
                      isDarkColorScheme ? "text-white" : "text-black"
                    )}
                  >
                    {item.title}
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      handleRemove(item);
                      if (selected.length <= 1) {
                        setShowSelectedModal(false);
                      }
                    }}
                    className="p-1"
                  >
                    <X size={16} color={isDarkColorScheme ? "#fff" : "#000"} />
                  </TouchableOpacity>
                </View>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  // Function to handle selecting/deselecting all items
  const handleSelectAll = () => {
    if (selected.length === data.length) {
      // If all items are already selected, deselect all
      setSelected([]);
      if (onSelect) {
        onSelect([]);
      }
    } else {
      // Otherwise, select all items
      setSelected([...data]);
      if (onSelect) {
        onSelect([...data]);
      }
    }
    // Close the dropdown after selection
    setDropdownVisible(false);
  };

  // Render the dropdown for selecting new items
  const renderDropdown = () => {
    // Get screen dimensions
    const screenWidth = Dimensions.get("window").width;
    // Check if all items are selected
    const allSelected = data.length > 0 && selected.length === data.length;

    return (
      <Modal
        visible={dropdownVisible && data.length > 0}
        transparent={true}
        animationType="none"
        onRequestClose={() => setDropdownVisible(false)}
      >
        <View className="flex-1 justify-start items-center">
          <TouchableWithoutFeedback onPress={() => setDropdownVisible(false)}>
            <View className="absolute top-0 left-0 right-0 bottom-0 bg-transparent" />
          </TouchableWithoutFeedback>

          <View
            className={cn(
              "border border-gray-500 rounded-md shadow-lg max-h-[300px] absolute z-[9999]",
              isDarkColorScheme ? "bg-[#222]" : "bg-white"
            )}
            style={{
              // Center in the screen horizontally
              left: screenWidth / 2 - comboBoxLayout.width / 2,
              width: comboBoxLayout.width,
              // Fixed position from top - adjust this value as needed to position after "Classes"
              top: 120, // This is a fixed value - adjust based on where your "Classes" element is
            }}
          >
            {/* Select All Option */}
            <TouchableOpacity
              className={cn(
                "p-2.5 border-b",
                isDarkColorScheme ? "border-[#444]" : "border-[#eee]",
                allSelected &&
                  (isDarkColorScheme
                    ? "bg-[rgba(76,175,80,0.2)]"
                    : "bg-[rgba(76,175,80,0.1)]")
              )}
              onPress={handleSelectAll}
            >
              <View className="flex-row items-center justify-between w-full">
                <Text
                  className={cn(
                    "text-sm font-bold flex-1",
                    isDarkColorScheme ? "text-white" : "text-black"
                  )}
                >
                  Select All
                </Text>
                {allSelected && <Check size={16} color="#4CAF50" />}
              </View>
            </TouchableOpacity>

            <FlatList
              data={data}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => {
                const isSelected = selected.some(
                  (selectedItem) => selectedItem.id === item.id
                );
                return (
                  <TouchableOpacity
                    className={cn(
                      "p-2.5 border-b",
                      isDarkColorScheme ? "border-[#444]" : "border-[#eee]",
                      isSelected &&
                        (isDarkColorScheme
                          ? "bg-[rgba(76,175,80,0.2)]"
                          : "bg-[rgba(76,175,80,0.1)]")
                    )}
                    onPress={() => handleSelect(item)}
                  >
                    <View className="flex-row items-center justify-between w-full">
                      <Text
                        className={cn(
                          "text-sm flex-1",
                          isDarkColorScheme ? "text-white" : "text-black"
                        )}
                      >
                        {item.title}
                      </Text>
                      {isSelected && <Check size={16} color="#4CAF50" />}
                    </View>
                  </TouchableOpacity>
                );
              }}
              ListEmptyComponent={
                <Text className="text-center p-2.5 text-gray-500">
                  No items available
                </Text>
              }
            />
          </View>
        </View>
      </Modal>
    );
  };

  // We'll use a simpler approach with a TouchableWithoutFeedback overlay
  // instead of trying to use global event listeners

  return (
    <View className="w-full relative">
      {/* Main combo box container */}
      <TouchableOpacity
        className="w-full border border-gray-500 rounded-md min-h-[38px] h-[45px] relative"
        activeOpacity={0.7}
        onPress={() => setDropdownVisible(!dropdownVisible)}
        onLayout={(event) => {
          const { y, height, x, width } = event.nativeEvent.layout;
          setComboBoxLayout({ y, height, x, width });
        }}
      >
        {/* Selected items summary display */}
        {selected.length === 0 ? (
          <View className="flex-row items-center h-full px-2 py-2 relative">
            <Text
              className={cn(
                "text-sm",
                isDarkColorScheme ? "text-white" : "text-black"
              )}
            >
              {placeholder ?? "Select items..."}
            </Text>

            {/* Dropdown indicator chevron - absolute positioned for empty state */}
            <View className="absolute right-2 flex-row items-center">
              <ChevronDown
                size={16}
                color={isDarkColorScheme ? "white" : "black"}
                className="opacity-70"
              />
            </View>
          </View>
        ) : (
          <View className="flex-row items-center px-2 py-2 h-full">
            <Text
              className={cn(
                "text-sm flex-1",
                isDarkColorScheme ? "text-white" : "text-black"
              )}
            >
              {`${selected[0].title}${
                selected.length > 1 ? ` +${selected.length - 1}` : ""
              }`}
            </Text>

            <View className="flex-row items-center">
              {/* Remove first item button */}
              <TouchableOpacity
                onPress={(e) => {
                  e.stopPropagation();
                  handleRemove(selected[0]);
                }}
                className="p-1 ml-1"
              >
                <X size={16} color={isDarkColorScheme ? "white" : "black"} />
              </TouchableOpacity>

              {/* Show all selected items button */}
              {selected.length > 1 && (
                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation();
                    setShowSelectedModal(true);
                  }}
                  className="p-1 ml-1"
                >
                  <ChevronDown
                    size={16}
                    color={isDarkColorScheme ? "white" : "black"}
                  />
                </TouchableOpacity>
              )}

              {!selected.length && (
                <ChevronDown
                  size={16}
                  color={isDarkColorScheme ? "white" : "black"}
                  className="ml-2 opacity-70"
                />
              )}
            </View>
          </View>
        )}
      </TouchableOpacity>

      {/* Dropdown for selecting new items - using Modal to ensure it's above everything */}
      {renderDropdown()}

      {/* Modal for showing all selected items */}
      {renderSelectedItemsModal()}
    </View>
  );
};
