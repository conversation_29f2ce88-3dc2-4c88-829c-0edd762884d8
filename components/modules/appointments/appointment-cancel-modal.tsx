import React, { useState, useEffect } from "react";
import { View } from "react-native";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useCancelReservation } from "~/modules/classes/mutations/useCancelReservation";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
// import {
//   getCalendarEventId,
//   removeCalendarEventId,
// } from "~/modules/calendar/calendar-storage";
// import { deleteCalendarEvent } from "~/modules/calendar/calendar-utils";
import { showSuccessToast } from "~/components/toast";

interface AppointmentCancelModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  appointmentId: number;
}

export function AppointmentCancelModal({
  isOpen,
  setIsOpen,
  appointmentId,
}: AppointmentCancelModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  // Calendar functionality commented out
  // const [calendarEventId, setCalendarEventId] = useState<string | null>(null);
  // const [isLoadingCalendarId, setIsLoadingCalendarId] = useState(true);

  // // Fetch the calendar event ID when the modal opens
  // useEffect(() => {
  //   if (isOpen) {
  //     const fetchCalendarEventId = async () => {
  //       setIsLoadingCalendarId(true);
  //       const eventId = await getCalendarEventId(appointmentId);
  //       setCalendarEventId(eventId);
  //       setIsLoadingCalendarId(false);
  //     };

  //     fetchCalendarEventId();
  //   }
  // }, [isOpen, appointmentId]);

  const { mutate: handleCancellation, isPending: isCancelling } =
    useCancelReservation(async () => {
      // Calendar functionality commented out
      // // If there's a calendar event ID, delete the calendar event
      // if (calendarEventId) {
      //   const deleted = await deleteCalendarEvent(calendarEventId);
      //   if (deleted) {
      //     // Remove the calendar event ID from storage
      //     await removeCalendarEventId(appointmentId);
      //     showSuccessToast("Appointment and calendar event deleted");
      //   } else {
      //     showSuccessToast(
      //       "Appointment deleted, but calendar event could not be deleted"
      //     );
      //   }
      // } else {
      //   showSuccessToast("Appointment deleted");
      // }

      // Simplified implementation
      showSuccessToast("Appointment deleted");

      // Track the deletion event
      trackEvent(EVENTS.DELETE_APPOINTMENT, {
        appointment_id: appointmentId,
        calendar_event_deleted: false, // Always false since calendar functionality is disabled
      });

      setIsOpen(false);
    });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="w-[85vw] max-w-[320px] rounded-2xl p-0 overflow-hidden bg-white/95 border-0 shadow-lg">
        <View className="p-5">
          <DialogHeader>
            <DialogTitle className="text-center text-gray-800 font-bold text-lg">
              Cancel Appointment?
            </DialogTitle>
            <DialogDescription className="text-red-500 mt-2 text-center">
              This will cancel the appointment permanently and cannot be undone.
              {/* Calendar functionality commented out */}
              {/* {calendarEventId && !isLoadingCalendarId && (
                <Text className="text-gray-600 mt-2 text-sm pl-2">
                  The appointment will also be removed from your calendar.
                </Text>
              )} */}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex flex-row mt-4 gap-2">
            <Button
              onPress={() => handleCancellation(appointmentId)}
              disabled={isCancelling}
              variant={"destructive"}
              className="flex-1 rounded-lg h-10 justify-center"
              textClassName="text-white font-medium text-sm"
              label={isCancelling ? "Cancelling..." : "Cancel"}
            />

            <DialogClose asChild>
              <Button
                className="flex-1 bg-gray-100 rounded-lg h-10 justify-center"
                textClassName="text-gray-700 text-sm"
                label="Keep"
                variant="secondary"
              />
            </DialogClose>
          </DialogFooter>
        </View>
      </DialogContent>
    </Dialog>
  );
}
