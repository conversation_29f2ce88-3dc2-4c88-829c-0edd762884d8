import * as React from "react";
import { View } from "react-native";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "~/components/ui/tabs";
import { useState } from "react";
import {
  ReservationProps,
  Reservations,
} from "~/components/modules/classes/reservations";
import { Waitlist } from "~/components/modules/classes/waitlists";
import { SearchInput } from "~/components/modules/classes/search-input";
import { matchSorter } from "match-sorter";
import { ClassDetailsResponse, Reservation } from "~/modules/classes/types";

enum ReservationWaitlistType {
  RESERVATION = "reservation",
  WAITLIST = "waitlist",
}

export function ReservationWaitlist({
  reservations,
  celebrations,
  waitlist,
}: ReservationProps) {
  const [value, setValue] = useState(ReservationWaitlistType.RESERVATION);

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData =
    value === ReservationWaitlistType.RESERVATION
      ? matchSorter(reservations ?? [], searchTerm, {
          keys: ["user_first_name", "user_last_name"],
        })
      : matchSorter(waitlist ?? [], searchTerm, {
          keys: ["first_name", "last_name"],
        });

  return (
    <View className="flex-1 w-full pt-6">
      <SearchInput
        className="mt-6"
        placeholder={`Search for ${value}`}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />
      <Tabs
        value={value}
        onValueChange={(newValue) =>
          setValue(newValue as ReservationWaitlistType)
        }
        className="w-full mx-auto flex-col gap-1.5"
      >
        <TabsList className="flex-row w-full gap-2">
          <TabsTrigger
            value={ReservationWaitlistType.RESERVATION}
            className="flex-1 rounded-lg"
            label={`RESERVATIONS (${reservations?.length})`}
          />

          <TabsTrigger
            value={ReservationWaitlistType.WAITLIST}
            className="flex-1 rounded-lg"
            label={`WAITLISTS (${waitlist?.length})`}
          />
        </TabsList>

        <TabsContent value={ReservationWaitlistType.RESERVATION}>
          <Reservations
            reservations={filteredData as Reservation[]}
            celebrations={celebrations}
          />
        </TabsContent>
        <TabsContent value={ReservationWaitlistType.WAITLIST}>
          <Waitlist waitlist={filteredData as ClassDetailsResponse[]} />
        </TabsContent>
      </Tabs>
    </View>
  );
}
