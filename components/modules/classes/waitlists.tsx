import React, { Fragment, useState } from "react";
import { TouchableOpacity, View } from "react-native";

import { FlashList } from "@shopify/flash-list";

import { ClassDetailsResponse } from "~/modules/classes/types";
import { Text } from "~/components/ui/text";
import { Card, CardContent } from "~/components/ui/card";
import { BaseAvatar } from "./avatar";
import { getInitials } from "~/modules/classes/utils";

import { AddWaitlistToClassModal } from "./add-waitlist-to-class-modal";

import AntDesign from "@expo/vector-icons/AntDesign";
import { EmptyState } from "./reservations";

const WaitlistCard = ({
  name,
  id,
  image,
  position,
}: {
  name: string;
  id: number;
  image?: string;
  position?: number;
}) => {
  const [openModal, setOpenModal] = useState(false);

  return (
    <Fragment>
      <Card className="w-full mt-4 mb-3">
        <CardContent className="p-0 flex flex-row justify-between">
          <View className="flex flex-row ml-0  mt-2 pl-2 p-1">
            <BaseAvatar url={image} name={getInitials(name)} />
            <Text className="font-bold mt-2 pl-2">{name}</Text>
          </View>
          <Text>{position}</Text>
          <View className={"flex flex-row items-center gap-4"}>
            <TouchableOpacity
              onPress={() => setOpenModal(true)}
              className="px-4 bg-[#E4E7EC] h-full flex items-center justify-center"
            >
              <AntDesign
                onPress={() => setOpenModal(true)}
                name="plus"
                size={20}
                color="black"
              />
            </TouchableOpacity>
          </View>
        </CardContent>
      </Card>
      {openModal && (
        <AddWaitlistToClassModal
          isOpen={openModal}
          setIsOpen={setOpenModal}
          waitlistId={id}
        />
      )}
    </Fragment>
  );
};

export function Waitlist({ waitlist }: { waitlist?: ClassDetailsResponse[] }) {
  return (
    <View className="flex flex-1">
      {!waitlist?.length ? (
        <EmptyState label="No waitlist data available" />
      ) : (
        <FlashList
          className="h-full overflow-scroll"
          data={waitlist}
          renderItem={({ item }) => (
            <WaitlistCard
              name={`${item?.first_name} ${item?.last_name}`}
              id={item.id}
              image={item?.image}
              position={item?.position}
            />
          )}
          estimatedItemSize={87}
        />
      )}
    </View>
  );
}
