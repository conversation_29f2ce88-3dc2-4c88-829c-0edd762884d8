{"expo": {"name": "Upace Connect", "slug": "upace-instructor", "version": "1.2.8", "orientation": "portrait", "icon": "./assets/images/appstore.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/upace-splash.png", "resizeMode": "contain"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.anonymous.upaceinstructor", "googleServicesFile": "./lib/GoogleService-Info.plist", "buildNumber": "8", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIAccessibilityIsVoiceOverRunning": true, "UIAccessibilityIsBoldTextEnabled": true, "UIAccessibilityIsGrayscaleEnabled": true, "UIAccessibilityIsInvertColorsEnabled": true, "UIAccessibilityIsReduceTransparencyEnabled": true, "UIAccessibilityIsReduceMotionEnabled": true, "NSCalendarsUsageDescription": "This app needs access to your calendar to add appointments", "NSRemindersUsageDescription": "This app needs access to your reminders to set appointment reminders"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/appstore.png", "backgroundColor": "#ffffff"}, "package": "com.anonymous.upaceinstructor", "googleServicesFile": "./lib/google-services.json", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/appstore.png"}, "plugins": ["@react-native-firebase/app", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}], "expo-router", ["expo-secure-store", {"faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-calendar", {"calendarPermission": "Allow $(PRODUCT_NAME) to access your calendar"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "1c4c7f97-3588-4b3b-9226-111b2593653b"}}}}