{"name": "upace-instructor", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c --ios", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-firebase/analytics": "^21.13.0", "@react-native-firebase/app": "^21.13.0", "@react-native-picker/picker": "2.7.5", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.0.2", "@rn-primitives/avatar": "~1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/label": "^1.1.0", "@rn-primitives/portal": "~1.1.0", "@rn-primitives/progress": "~1.1.0", "@rn-primitives/select": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/tooltip": "~1.1.0", "@rn-primitives/types": "^1.1.0", "@shopify/flash-list": "1.6.4", "@shopify/react-native-skia": "1.2.3", "@tanstack/query-sync-storage-persister": "^5.59.16", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-persist-client": "^5.59.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "expo": "~51.0.39", "expo-blur": "~13.0.3", "expo-build-properties": "~0.12.5", "expo-calendar": "~13.0.5", "expo-camera": "~15.0.16", "expo-dev-client": "~4.0.29", "expo-image": "~1.13.0", "expo-linear-gradient": "~13.0.2", "expo-linking": "~6.3.1", "expo-navigation-bar": "~3.0.7", "expo-router": "~3.5.24", "expo-secure-store": "~13.0.2", "expo-splash-screen": "^0.27.7", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "i": "^0.3.7", "ky": "^1.7.5", "lodash": "^4.17.21", "lottie-react-native": "6.7.0", "lucide-react-native": "^0.378.0", "match-sorter": "^7.0.0", "nativewind": "^4.0.33", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.53.1", "react-native": "0.74.5", "react-native-autocomplete-dropdown": "^4.0.0", "react-native-gesture-handler": "~2.16.1", "react-native-keyboard-controller": "^1.14.3", "react-native-mmkv": "2.12.2", "react-native-modal-datetime-picker": "^18.0.0", "react-native-reanimated": "~3.10.1", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "4.10.5", "react-native-screens": "~3.31.1", "react-native-svg": "15.2.0", "react-native-web": "~0.19.6", "react-native-webview": "13.8.6", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/lodash": "^4.17.13", "@types/react": "~18.2.79", "typescript": "~5.3.3"}, "private": true}