import { useLocalSearchParams, useNavigation } from "expo-router";
import { View } from "react-native";

import { Text } from "~/components/ui/text";

import {
  KeyboardAwareScrollView,
  KeyboardToolbar,
} from "react-native-keyboard-controller";

import { useEffect, useMemo, useState } from "react";
import { BottomSheet } from "~/components/modules/classes/bottom-sheet";

import { Button } from "~/components/ui/button";

import { useUserMembership } from "~/modules/classes/queries/useUserMembership";

import { useCreateReservation } from "~/modules/classes/mutations/useCreateReservation";
import { isEmpty } from "lodash/fp";

import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";
import { SearchUsers } from "~/modules/search-users";
import { formatClassDate } from "~/modules/classes/utils";
import { ComboBox } from "~/components/modules/common/Combo-box";

export default function AddReservation() {
  const navigation = useNavigation();

  const { id: classId, date } = useLocalSearchParams<{
    id: string;
    date?: string;
  }>();

  const [selectedUser, setSelectedUser] =
    useState<AutocompleteDropdownItem | null>();

  const [member, setMember] = useState<AutocompleteDropdownItem | null>();

  const { data: memberShipData = [] } = useUserMembership({
    itemId: classId,
    userId: selectedUser?.id as string,
    type: "class",
  });

  const { mutate: createReservation, isPending: isCreating } =
    useCreateReservation(() => navigation.goBack());

  const memberships = useMemo(
    () =>
      memberShipData?.map((item) => ({
        id: item?.id as string,
        title: item?.package_name as string,
      })),
    [memberShipData]
  );

  const handleReservation = () => {
    if (isEmpty(selectedUser) || isEmpty(memberships)) {
      return;
    }

    return createReservation({
      user_id: selectedUser.id,
      class_id: classId,
      membership_id: (member?.id as string) || memberships[0].id,
      type: "class",
      is_virtual: false,
      date: formatClassDate(date),
    });
  };

  useEffect(() => {
    if (!isEmpty(memberships)) {
      setMember(memberships[0]);
    }
    if (isEmpty(selectedUser)) {
      setMember(null);
    }
  }, [memberships]);

  return (
    <AutocompleteDropdownContextProvider>
      <BottomSheet>
        <View className="gap-3 pt-4 relative">
          <Text className="text-center  font-bold text-lg mb-4">
            Add Reservation
          </Text>
          <KeyboardAwareScrollView
            bottomOffset={10}
            contentContainerStyle={{
              gap: 16,
              paddingTop: 16,
              paddingBottom: 30,
            }}
          >
            <SearchUsers position="up" onSelect={setSelectedUser} />
            <View className="h-14">
              <ComboBox
                onSelect={setMember}
                data={memberships}
                initialValue={String(member?.id)}
                placeholder="Select membership"
                value={member?.title ?? ""}
                direction="up"
                emptyText="No available membership to select"
              />
            </View>
          </KeyboardAwareScrollView>

          <Button
            onPress={handleReservation}
            disabled={isEmpty(selectedUser) || isEmpty(member)}
            isLoading={isCreating}
            label="Add reservation"
          />
          <KeyboardToolbar />
        </View>
      </BottomSheet>
    </AutocompleteDropdownContextProvider>
  );
}
