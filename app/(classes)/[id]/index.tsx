import { router, useLocalSearchParams } from "expo-router";
import { useCallback, useEffect } from "react";
import { RefreshControl, ScrollView, View, SafeAreaView } from "react-native";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import { useClassByIdQuery } from "~/modules/classes/queries/useClassbyIdQuery";

import { Loader } from "~/components/modules/classes/loader";

import { ActionChips } from "~/components/modules/classes/action-chips";
import { StatsBanner } from "~/components/modules/classes/stats-banner";
import { ClassHeader } from "~/components/modules/classes/class-header";
import { useWaitlistQuery } from "~/modules/classes/queries/useWaitlistQuery";

import { Celebration } from "~/components/modules/classes/celebrations/celebration";
import { ReservationWaitlist } from "~/components/modules/classes/reservation-waitlist";

export default function Details() {
  const { name, start_time, end_time, room_name, date, id } =
    useLocalSearchParams<{
      id: string;
      name: string;
      start_time: string;
      end_time: string;
      room_name: string;
      date?: string;
      cancelled?: string;
    }>();

  const { data: foundData, isPending, refetch } = useClassByIdQuery();
  const { trackEvent, EVENTS } = useAnalytics();

  // Track class view
  useEffect(() => {
    if (id && name) {
      trackEvent(EVENTS.VIEW_CLASS, {
        class_id: id,
        class_name: name,
        room_name,
        date,
      });
    }
  }, [id, name]);

  const spotsAvailable = Math.abs(
    (foundData?.class?.spots ?? 0) - (foundData?.reservations?.length ?? 0)
  );

  const {
    data: waitlistData,
    isPending: isLoading,
    refetch: refetchWaitlist,
  } = useWaitlistQuery();

  const onRefresh = useCallback(() => {
    refetch();
    refetchWaitlist();
  }, []);

  return (
    <ScreenTracker screenName="ClassDetails" params={{ id, name }}>
      <SafeAreaView className="flex-1 mt-0">
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={isPending || isLoading}
              onRefresh={onRefresh}
            />
          }
        >
          <View className="flex p-3 mt-2 flex-1 overflow-auto h-full">
            <ClassHeader
              name={name}
              startTime={start_time}
              endTime={end_time}
              roomName={room_name}
              is_class_subbed={foundData?.class?.is_class_subbed}
              cancelled={foundData?.class?.cancelled}
              date={date}
            />

            <StatsBanner
              reservationsCount={foundData?.reservations?.length}
              waitlistCount={waitlistData?.length}
              spotsAvailable={spotsAvailable}
            />

            <View className="mt-4">
              <ActionChips
                classId={id}
                className={name}
                onAddReservation={() =>
                  router.push({
                    pathname: "/(classes)/[id]/add-reservation",
                    params: {
                      id: foundData?.class.id as number,
                      date,
                    },
                  })
                }
                onOccupancyPress={() =>
                  router.push({
                    pathname: "/(classes)/[id]/occupancy",
                    params: {
                      ...foundData?.occupancy,
                      id: foundData?.class.id as number,
                      walkInSpots: foundData?.class.walkin_spots,
                      date,
                    },
                  })
                }
                onBarcodePress={() =>
                  router.push({
                    pathname: "/(classes)/[id]/barcode",
                    params: {
                      id: foundData?.class.id as number,
                    },
                  })
                }
                onClassPress={() => router.push("/(classes)/(tabs)/classes")}
              />
            </View>

            {isPending ? (
              <Loader className="mt-4" />
            ) : (
              <ReservationWaitlist
                waitlist={waitlistData}
                celebrations={foundData?.celebrations}
                reservations={foundData?.reservations}
              />
            )}
          </View>

          <Celebration
            celebrations={foundData?.celebrations}
            classId={foundData?.class?.id}
          />
        </ScrollView>
      </SafeAreaView>
    </ScreenTracker>
  );
}
