import * as z from "zod";

import { router } from "expo-router";
import {
  AddAppointmentForm,
  AppointmentSchema,
} from "~/components/modules/appointments/appointment-form";
import { useCreateReservation } from "~/modules/classes/mutations/useCreateReservation";
import { useSession } from "~/modules/login/auth-provider";
import { formatClassDate } from "~/modules/classes/utils";
import { format, addDays } from "date-fns";
import { useGenericData } from "~/modules/appointments/queries/useGenericData";
import { useCheckInMutation } from "~/modules/classes/mutations/useCheckInMutation";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";
import { generateRecurringAppointments } from "~/modules/appointments/utils/recurring-appointments";
import { Alert } from "react-native";
import { useEffect, useRef } from "react"; // useState removed - calendar functionality commented out
// Calendar functionality commented out
// import { AddToCalendarModal } from "~/components/modules/calendar/add-to-calendar-modal";
// import { AddMultipleToCalendarModal } from "~/components/modules/calendar/add-multiple-to-calendar-modal";
import {
  ApiResponse,
  // CalendarAppointment,
  HttpErrorResponse,
  SuccessfulAppointment,
} from "~/modules/appointments/types/calendar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

export default function AppointmentRequest() {
  let recurringRef = useRef<boolean>();

  // Calendar functionality commented out
  // const [showCalendarModal, setShowCalendarModal] = useState(false);
  // const [showMultipleCalendarModal, setShowMultipleCalendarModal] = useState(false);

  // Initialize form with React Hook Form
  const form = useForm<z.infer<typeof AppointmentSchema>>({
    resolver: zodResolver(AppointmentSchema),
    defaultValues: {
      date: new Date(),
      checkInReservation: false,
      recurring: false,
      equipmentType: null,
      recurringEndDate: new Date(addDays(new Date(), 7)),
      recurringDays: {
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false,
      },
    },
  });

  // Reset form when component mounts or unmounts
  useEffect(() => {
    // Reset form when component mounts
    resetForm();

    // Reset form when component unmounts
    return () => {
      resetForm();
    };
  }, []);

  // Function to reset the form
  const resetForm = () => {
    form.reset({
      date: new Date(),
      checkInReservation: false,
      recurring: false,
      equipmentType: null,
      recurringEndDate: new Date(addDays(new Date(), 7)),
      recurringDays: {
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false,
      },
      // Explicitly clear these fields to ensure they're reset
      equipment: undefined,
      user: undefined,
      membership: undefined,
      startTime: undefined,
    });
  };

  // Calendar functionality commented out
  // const [reservationDetails, setReservationDetails] = useState<{
  //   title: string;
  //   startTime: string;
  //   endTime: string;
  //   location?: string;
  //   notes?: string;
  //   id?: number | string;
  //   type?: string;
  // } | null>(null);

  // const [multipleAppointments, setMultipleAppointments] = useState<
  //   Array<{
  //     id: string | number;
  //     title: string;
  //     startTime: string;
  //     endTime: string;
  //     location?: string;
  //     notes?: string;
  //     type?: string;
  //   }>
  // >([]);

  const { mutateAsync: createReservation, isPending } = useCreateReservation(
    (_data) => {
      // Reset the form after successful submission
      resetForm();

      // For recurring appointments, we'll handle calendar integration separately
      if (recurringRef.current) {
        return;
      }

      // For single appointments, redirect to appointments page
      // Calendar functionality commented out
      // if (data?.res_id) {
      //   setReservationDetails((currentDetails) => {
      //     if (currentDetails) {
      //       return {
      //         ...currentDetails,
      //         id: data.res_id,
      //       };
      //     }
      //     return currentDetails;
      //   });
      //   setShowCalendarModal(true);
      // } else {
      //   router.push("/(classes)/(tabs)/appointments");
      // }

      // Always redirect to appointments page
      router.push("/(classes)/(tabs)/appointments");
    },
    Boolean(recurringRef.current)
  );

  const { mutate: checkIn } = useCheckInMutation();
  const { trackEvent, EVENTS } = useAnalytics();

  const { data: sessionData } = useSession();

  const onSubmit = async (formData: z.infer<typeof AppointmentSchema>) => {
    try {
      recurringRef.current = formData.recurring;
      // Check if this is a recurring appointment

      if (
        formData.recurring &&
        formData.recurringEndDate &&
        formData.recurringDays
      ) {
        // Generate all recurring appointments
        const appointments = generateRecurringAppointments(formData);

        // Show confirmation dialog with the number of appointments
        Alert.alert(
          "Confirm Recurring Appointments",
          `You are about to create ${
            appointments.length + 1
          } appointments. Do you want to continue?`,
          [
            {
              text: "Cancel",
              style: "cancel",
            },
            {
              text: "Create",
              onPress: async () => {
                // Initialize arrays and counters for tracking appointments
                const errors: { date: string; error: string }[] = [];
                let successCount = 0;
                const successfulAppointments: SuccessfulAppointment[] = [];

                // Create the first appointment (original date)
                const firstResp = await createReservation({
                  type: "pt",
                  equipment_id: formData.equipment.id,
                  membership_id: formData.membership?.id as string,
                  trainer_id: sessionData?.id as string,
                  start_time: `${formatClassDate(formData.date)} ${
                    formData.startTime.id
                  }`,
                  user_id: formData.user.id,
                  date: formatClassDate(formData.date),
                  override_membership: formData.membership?.id ? false : true,
                });

                // Store the first appointment for calendar integration if successful
                if (firstResp.success && (firstResp as ApiResponse).res_id) {
                  // Add to successful appointments list
                  successfulAppointments.push({
                    res_id: (firstResp as ApiResponse).res_id || 0,
                    date: formData.date,
                    equipment: formData.equipment,
                    user: formData.user,
                    startTime: formData.startTime,
                    membership: formData.membership,
                    equipmentType: formData.equipmentType,
                  });

                  // Increment success count for the first appointment
                  successCount++;
                }

                // Check in the first appointment if needed
                if (
                  formData.checkInReservation &&
                  (firstResp as ApiResponse)?.res_id !== undefined
                ) {
                  checkIn({
                    id: (firstResp as ApiResponse).res_id as number,
                    isCheckIn: true,
                    type: "pt",
                  });
                }

                for (let i = 0; i < appointments.length; i++) {
                  try {
                    const appointment = appointments[i];
                    const formattedDate = formatClassDate(appointment.date);

                    const result = await createReservation({
                      type: "pt",
                      equipment_id: appointment.equipment.id,
                      membership_id: appointment.membership?.id as string,
                      trainer_id: sessionData?.id as string,
                      start_time: `${formattedDate} ${appointment.startTime.id}`,
                      user_id: appointment.user.id,
                      date: formattedDate,
                      equ_court_id: formData.equipmentType?.id as string,
                      override_membership: appointment.membership?.id
                        ? false
                        : true,
                    });

                    if (!result.success) {
                      // Get detailed error message from the backend
                      let errorDetail = result.message || "Unknown error";

                      // Make sure we're not getting a generic message
                      if (errorDetail === "Unable to create reservation") {
                        // Check for additional details in the result
                        const resultWithDetails = result as any;
                        if (resultWithDetails.details) {
                          errorDetail = resultWithDetails.details;
                        } else if (resultWithDetails.error) {
                          errorDetail =
                            typeof resultWithDetails.error === "string"
                              ? resultWithDetails.error
                              : JSON.stringify(resultWithDetails.error);
                        }
                      }

                      // Add time information to make the error more specific
                      const timeInfo = appointment.startTime.title;

                      errors.push({
                        date: format(appointment.date, "EEE, MMM d, yyyy"),
                        error: `${timeInfo}: ${errorDetail}`,
                      });
                    } else {
                      successCount++;

                      // Store successful appointment for calendar integration
                      successfulAppointments.push({
                        res_id: (result as ApiResponse).res_id || 0,
                        date: appointment.date,
                        equipment: appointment.equipment,
                        user: appointment.user,
                        startTime: appointment.startTime,
                        membership: appointment.membership,
                        equipmentType: appointment.equipmentType,
                      });
                    }
                  } catch (err) {
                    // Try to extract error message from the error object
                    let errorMessage = "Failed to create appointment";

                    if (err instanceof Error) {
                      errorMessage = err.message;
                    } else if (typeof err === "object" && err !== null) {
                      // Try to extract message from error object
                      const error = err as HttpErrorResponse;

                      // Check for response object (HTTP errors)
                      if (
                        error.response &&
                        typeof error.response.json === "function"
                      ) {
                        try {
                          // Try to get the error message from the response
                          const responseData = await error.response.json();
                          if (responseData && responseData.message) {
                            errorMessage = responseData.message;
                          }
                        } catch (jsonError) {
                          console.error(
                            "Error parsing response JSON:",
                            jsonError
                          );
                        }
                      } else if (error.message) {
                        errorMessage = error.message || "";
                      } else if (error.error) {
                        errorMessage =
                          typeof error.error === "string"
                            ? error.error
                            : JSON.stringify(error.error);
                      }
                    }

                    // Add time information to make the error more specific
                    const timeInfo = appointments[i].startTime.title;

                    errors.push({
                      date: format(appointments[i].date, "EEE, MMM d, yyyy"),
                      error: `${timeInfo}: ${errorMessage}`,
                    });
                  }
                }

                // Track recurring appointment creation
                trackEvent(EVENTS.CREATE_APPOINTMENT, {
                  equipment_id: formData.equipment.id,
                  user_id: formData.user.id,
                  date: formatClassDate(formData.date),
                  time: formData.startTime.title,
                  check_in: formData.checkInReservation,
                  recurring: true,
                  recurring_count: appointments.length + 1, // +1 for the original appointment
                  success_count: successCount, // Already includes the original appointment if successful
                  error_count: errors.length,
                });

                // Calendar functionality commented out
                // Prepare appointment details for calendar (for successful appointments)
                // if (successfulAppointments.length > 0) {
                //   const calendarAppointments: CalendarAppointment[] =
                //     successfulAppointments.map((appointment) => {
                //       // Parse the start time
                //       const [startHour, startMinute] = appointment.startTime.id
                //         .split(":")
                //         .map(Number);

                //       // Create Date objects for start and end times
                //       const startDate = new Date(appointment.date);
                //       startDate.setHours(startHour, startMinute, 0, 0);

                //       // End time is 1 hour after start time
                //       const endDate = new Date(startDate);
                //       endDate.setHours(startDate.getHours() + 1);

                //       return {
                //         id: appointment.res_id,
                //         title: `Appointment with ${
                //           appointment.user.title || "Member"
                //         }`,
                //         startTime: startDate.toISOString(),
                //         endTime: endDate.toISOString(),
                //         location: appointment.equipment.title || "",
                //         notes: `Personal training appointment with ${
                //           appointment.user.title || "Member"
                //         }`,
                //         type: "pt",
                //       };
                //     });

                //   // Set the appointments for the calendar modal
                //   setMultipleAppointments(calendarAppointments);

                //   // Show the calendar modal directly
                //   setShowMultipleCalendarModal(true);
                // }

                // Always redirect to appointments page if there were successful appointments
                if (successfulAppointments.length > 0) {
                  router.push("/(classes)/(tabs)/appointments");
                }

                // Reset the form after successful submission
                if (successCount > 0) {
                  resetForm();
                }

                // Show error summary if there were any errors
                if (errors.length > 0) {
                  // Format error message
                  let errorMessage = `Successfully created ${successCount} of ${
                    appointments.length + 1
                  } appointments.\n\nThe following appointments failed:\n`;

                  // Group errors by date for better readability
                  const errorsByDate: Record<string, string[]> = {};

                  errors.forEach((error) => {
                    if (!errorsByDate[error.date]) {
                      errorsByDate[error.date] = [];
                    }
                    errorsByDate[error.date].push(error.error);
                  });

                  // Format the grouped errors
                  Object.entries(errorsByDate).forEach(([date, errorList]) => {
                    errorMessage += `\n• ${date}:`;
                    errorList.forEach((err) => {
                      errorMessage += `\n  - ${err}`;
                    });
                  });

                  Alert.alert(
                    "Appointment Creation Summary",
                    errorMessage,
                    [
                      {
                        text: "OK",
                        onPress: () => {
                          // If we're not showing the calendar modal (no successful appointments),
                          // navigate back to appointments
                          if (successfulAppointments.length === 0) {
                            router.push("/(classes)/(tabs)/appointments");
                          }
                        },
                      },
                    ],
                    { cancelable: false }
                  );
                }
              },
            },
          ]
        );
        return;
      }

      // Handle non-recurring appointment

      // Format date and time for API request
      const formattedDate = formatClassDate(formData.date);
      const startTime = `${formattedDate} ${formData.startTime.id}`;

      // Calendar functionality commented out
      // Set reservation details for calendar modal
      // try {
      //   // Parse the start time
      //   const [startHour, startMinute] = formData.startTime.id
      //     .split(":")
      //     .map(Number);

      //   // Create Date objects for start and end times
      //   const startDate = new Date(formData.date);
      //   startDate.setHours(startHour, startMinute, 0, 0);

      //   // End time is 1 hour after start time
      //   const endDate = new Date(startDate);
      //   endDate.setHours(startDate.getHours() + 1);

      //   // Format as ISO strings for better compatibility
      //   const startTimeISO = startDate.toISOString();
      //   const endTimeISO = endDate.toISOString();

      //   setReservationDetails({
      //     title: `Appointment with ${formData.user.title || "Member"}`,
      //     startTime: startTimeISO,
      //     endTime: endTimeISO,
      //     location: formData.equipment.title || "",
      //     notes: `Personal training appointment with ${
      //       formData.user.title || "Member"
      //     }`,
      //     type: "pt",
      //   });
      // } catch (err: unknown) {
      //   const error = err as Error;
      //   console.error("Error formatting calendar dates:", error);
      //   // Still create the appointment even if calendar formatting fails
      // }

      const resp = await createReservation({
        type: "pt",
        equipment_id: formData.equipment.id,
        membership_id: formData.membership?.id as string,
        trainer_id: sessionData?.id as string,
        start_time: startTime,
        user_id: formData.user.id,
        date: formattedDate,
        override_membership: formData.membership?.id ? false : true,
        equ_court_id: formData.equipmentType?.id as string,
      });

      // Reset the form after successful submission
      if (resp.success) {
        resetForm();
      }

      // Track appointment creation
      trackEvent(EVENTS.CREATE_APPOINTMENT, {
        equipment_id: formData.equipment.id,
        user_id: formData.user.id,
        date: formatClassDate(formData.date),
        time: formData.startTime.title,
        check_in: formData.checkInReservation,
        recurring: false,
      });

      if (formData.checkInReservation && (resp as any)?.res_id) {
        // Note: checkIn doesn't return a Promise, so await has no effect
        checkIn({
          id: (resp as any).res_id,
          isCheckIn: true,
          type: "pt",
        });

        // Track check-in event
        trackEvent(EVENTS.CHECK_IN, {
          reservation_id: (resp as any).res_id,
          type: "appointment",
        });
      }
    } catch (error) {
      console.error("Error creating appointment:", error);
    }
  };

  const { data: equipmentData } = useGenericData();

  return (
    <ScreenTracker screenName="CreateAppointment">
      <AddAppointmentForm
        equipmentData={equipmentData}
        isLoading={isPending}
        onSubmit={onSubmit}
        form={form}
      />

      {/* Calendar functionality commented out */}
      {/* Single Appointment Calendar Modal */}
      {/* {reservationDetails && (
        <AddToCalendarModal
          isOpen={showCalendarModal}
          setIsOpen={(open) => {
            setShowCalendarModal(open);
            if (!open) {
              // Reset form before navigating away
              resetForm();
              router.push("/(classes)/(tabs)/appointments");
            }
          }}
          reservationDetails={reservationDetails}
        />
      )} */}

      {/* Multiple Appointments Calendar Modal */}
      {/* {multipleAppointments.length > 0 && (
        <AddMultipleToCalendarModal
          isOpen={showMultipleCalendarModal}
          setIsOpen={(open) => {
            setShowMultipleCalendarModal(open);
            if (!open) {
              // Reset form before navigating away
              resetForm();
              router.push("/(classes)/(tabs)/appointments");
            }
          }}
          appointments={multipleAppointments}
        />
      )} */}
    </ScreenTracker>
  );
}
